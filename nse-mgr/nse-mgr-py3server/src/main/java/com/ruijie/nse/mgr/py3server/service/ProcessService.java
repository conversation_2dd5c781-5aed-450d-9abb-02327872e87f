package com.ruijie.nse.mgr.py3server.service;


import com.ruijie.nse.common.utils.security.SecurityUtils;
import com.ruijie.nse.mgr.py3server.config.Py3ServerProperties;
import com.ruijie.nse.mgr.py3server.dto.ProcessDto;
import com.ruijie.nse.mgr.py3server.launcher.context.EnvironmentContext;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.io.file.PathUtil;
import org.springframework.stereotype.Service;
import oshi.SystemInfo;
import oshi.software.os.OSProcess;
import oshi.software.os.OperatingSystem;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Stream;

/**
 * python3每个process的资源情况
 * 支持获取进程及其所有子进程的资源累计，兼容Windows和Ubuntu系统
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProcessService {

    private final Py3ServerProperties py3ServerProperties;

    public boolean isValidPid(long pid) {
        OperatingSystem os = new SystemInfo().getOperatingSystem();
        OSProcess process = os.getProcess((int) pid);
        return process != null;
    }

    /**
     * 获取python3进程的资源情况（包含所有子进程）
     * 支持Windows和Ubuntu系统
     *
     * @param pid  process进程id
     * @return ProcessDto 包含进程及其所有子进程的资源累计
     */
    public ProcessDto getProcessInfo(long pid) throws IOException {
        OperatingSystem os = new SystemInfo().getOperatingSystem();
        OSProcess process = os.getProcess((int) pid);
        if(process == null) {
            throw new IllegalCallerException("该进程[" + pid + "]不存在");
        }

        // 获取进程树（包含主进程和所有子进程）
        List<OSProcess> processTree = getProcessTree(os, pid);
        log.debug("进程 {} 及其子进程共 {} 个", pid, processTree.size());

        // 累计所有进程的资源使用情况
        ProcessResourceInfo resourceInfo = calculateTotalResourceUsage(processTree);

        long diskUsage = getDiskUsage(SecurityUtils.getUserId());

        return ProcessDto.builder()
                .cpuUsage(resourceInfo.totalCpuUsage())
                .memoryUsage(resourceInfo.totalMemoryUsage())
                .diskUsage(diskUsage)
                .build();
    }


    /**
     * 获取进程树（包含主进程和所有子进程）
     * 使用递归方式遍历所有子进程，支持Windows和Ubuntu系统
     *
     * @param os 操作系统对象
     * @param rootPid 根进程ID
     * @return 进程树列表
     */
    private List<OSProcess> getProcessTree(OperatingSystem os, long rootPid) {
        List<OSProcess> processTree = new ArrayList<>();
        Set<Integer> visitedPids = new HashSet<>();

        // 获取系统中所有进程
        List<OSProcess> allProcesses = os.getProcesses();
        Map<Integer, OSProcess> processMap = new HashMap<>();

        // 构建进程映射表，便于快速查找
        for (OSProcess process : allProcesses) {
            processMap.put(process.getProcessID(), process);
        }

        // 递归收集进程树
        collectProcessTree(processMap, (int) rootPid, processTree, visitedPids);

        return processTree;
    }

    /**
     * 递归收集进程及其所有子进程
     *
     * @param processMap 进程映射表
     * @param pid 当前进程ID
     * @param processTree 进程树结果列表
     * @param visitedPids 已访问的进程ID集合，防止循环引用
     */
    private void collectProcessTree(Map<Integer, OSProcess> processMap, int pid,
                                  List<OSProcess> processTree, Set<Integer> visitedPids) {
        // 防止循环引用
        if (visitedPids.contains(pid)) {
            return;
        }

        OSProcess process = processMap.get(pid);
        if (process == null) {
            return;
        }

        visitedPids.add(pid);
        processTree.add(process);

        // 查找所有子进程
        for (OSProcess candidateChild : processMap.values()) {
            try {
                if (candidateChild.getParentProcessID() == pid) {
                    collectProcessTree(processMap, candidateChild.getProcessID(), processTree, visitedPids);
                }
            } catch (Exception e) {
                // 某些进程可能在获取父进程ID时出现异常，忽略并继续
                log.debug("获取进程 {} 的父进程ID时出现异常: {}", candidateChild.getProcessID(), e.getMessage());
            }
        }
    }

    /**
     * 计算进程树的总资源使用情况
     * 累计所有进程的CPU和内存使用量
     *
     * @param processTree 进程树列表
     * @return 资源使用情况汇总
     */
    private ProcessResourceInfo calculateTotalResourceUsage(List<OSProcess> processTree) {
        double totalCpuUsage = 0.0;
        long totalMemoryUsage = 0L;
        int validProcessCount = 0;

        for (OSProcess process : processTree) {
            try {
                // 累计CPU使用率
                double cpuUsage = process.getProcessCpuLoadCumulative() * 100.0;
                log.info("进程 {} (PID: {}) - CPU使用率: {}", process.getName(), process.getProcessID(), cpuUsage);
                if (cpuUsage >= 0) { // 有效的CPU使用率
                    totalCpuUsage += cpuUsage;
                    validProcessCount++;
                }

                // 累计内存使用量（RSS - Resident Set Size）
                long memoryUsage = process.getResidentSetSize();
                if (memoryUsage > 0) { // 有效的内存使用量
                    totalMemoryUsage += memoryUsage;
                }

                log.debug("进程 {} (PID: {}) - CPU: {}, 内存: {} bytes",
                         process.getName(), process.getProcessID(), cpuUsage, memoryUsage);

            } catch (Exception e) {
                // 某些进程可能在获取资源信息时出现异常，记录日志但继续处理其他进程
                log.debug("获取进程 {} 资源信息时出现异常: {}", process.getProcessID(), e.getMessage());
            }
        }

        log.info("进程树资源汇总 - 总进程数: {}, 有效CPU进程数: {}, 总CPU使用率: {}, 总内存使用: {} bytes",
                processTree.size(), validProcessCount, totalCpuUsage, totalMemoryUsage);

        return new ProcessResourceInfo(totalCpuUsage, totalMemoryUsage);
    }

    /**
     * 获取用户的磁盘使用量，对于用户来说。目前最大的使用量就是projects
     * @param userId
     * @return
     * @throws IOException
     */
    private long getDiskUsage(String userId) throws IOException {
        Path userDir = Paths.get(py3ServerProperties.getStoragePath(), "projects", userId);
        if(!PathUtil.exists(userDir, false)) {
            PathUtil.mkdir(userDir);
        }
        try (Stream<Path> fileWalk = Files.walk(userDir)) {
            return fileWalk.filter(p -> p.toFile().isFile())
                    .mapToLong(p -> p.toFile().length())
                    .sum();
        }
    }

    /**
     * 进程资源信息内部类
     * 用于封装进程树的资源使用汇总信息
     */
    private record ProcessResourceInfo(double totalCpuUsage, long totalMemoryUsage) {

    }

}
