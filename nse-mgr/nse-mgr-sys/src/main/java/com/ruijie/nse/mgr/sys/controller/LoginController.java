package com.ruijie.nse.mgr.sys.controller;


import com.ruijie.nse.common.annotation.Anonymous;
import com.ruijie.nse.common.dto.R;
import com.ruijie.nse.mgr.sys.dto.input.LoginInput;
import com.ruijie.nse.mgr.sys.dto.output.LoginOutput;
import com.ruijie.nse.mgr.sys.dto.output.QueuingStudentOutput;
import com.ruijie.nse.mgr.sys.service.LoginService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Anonymous
public class LoginController {

    private final LoginService loginService;


    @PostMapping("/login")
    public R<LoginOutput> login(@Valid @RequestBody LoginInput req) {
        return R.success(loginService.loginByAccount(req.getAccount(), req.getPassword()));
    }

    @GetMapping("/queueInfo/{userId}")
    public R<QueuingStudentOutput> getQueueInfo(@PathVariable String userId) {
        return R.success(loginService.getQueueInfo(userId));
    }

    @PostMapping("cancelQueuing/{userId}")
    public R<Boolean> cancelQueue(@PathVariable String userId) {
        return R.success(loginService.cancelQueuing(userId));
    }

}