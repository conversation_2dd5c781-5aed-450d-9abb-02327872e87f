package com.ruijie.nse.mgr.sys.dto.output;

import com.ruijie.nse.mgr.common.dto.BaseQueueStudent;
import lombok.Data;

@Data
public class QueuingStudentOutput extends BaseQueueStudent {

    private String name;

    private String account;

    /**
     * 执行登录时间
     */
    private long doLoginTime;

    /**
     * 已等待时间
     */
    private long waitingTimeMs;

    /**
     * 排队第几位
     */
    private int waitingCount;

    /**
     * 总排队人数
     */
    private int totalWaitingCount;

    /**
     * 预计登录时间
     */
    private long expectedLoginTime;

    /**
     * 预计登录剩余毫秒数
     */
    private long expectedLoginMs;

    /**
     * 排队成功
     */
    private boolean queueSuccessful;

    /**
     * 距离超时还剩毫秒数
     */
    private long timeoutMs;
}
